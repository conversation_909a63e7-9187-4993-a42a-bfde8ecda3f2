---
marp: true
theme: default
math: mathjax
header: "Circuit Analysis and Equation Generator"
footer: "2025-08-22 <PERSON><PERSON><PERSON>"
paginate: true
---

# Circuit Analysis and Equation Generator

**回路解析と数式自動生成ツール**

---

## 📋 概要

このツールは、回路ファイル（.cct）から自動的に数学的方程式を生成し、R、L、C混在回路の包括的解析を行います。特に、キルヒホッフの法則に基づく詳細な電流・電圧関係の導出と、**すべての回路**に対する一般化された特殊解析が特徴です。

---

## 🎯 特徴
### ✨ 機能
- **完全ノード解析**: 各ノードの全接続素子と詳細方程式を生成
- **動的電流関係解析**: 回路構成を自動判定し、適切な電流関係式を生成
- **詳細KCL解析**: グラウンドノードを含む全ノードの完全解析
- **素子別詳細方程式**: R, L, C各素子の電流・電圧・エネルギー方程式
- **一般化特殊解析**: すべての回路で包括的KVL、エネルギー、システム行列解析
- **多形式出力**: テキスト、LaTeX、特殊解析レポート

---

## 🚀 クイックスタート

### 1. 依存関係のインストール
```bash
pip install -r requirements.txt
```

### 2. 基本的な使用方法
```bash
python main.py <circuit_file>.cct [オプション]
```
---

### 素子別詳細方程式

#### 抵抗器（R）
- **電流**: ${I_r = V_r / R}$（オームの法則）
- **電力**: ${P_r = V_r {\times} I_r = V_r² / R}$

#### インダクタ（L）
- **電圧-電流関係**: ${V_l = L {\times} \frac{d(I_l)}{dt}}$
- **離散時間**: ${I_l = I_{l\_old} + \frac{V_l {\times} dt}{L}}$
- **エネルギー**: ${E_l = \frac{1}{2} {\times} L {\times} I_l²}$

---

#### コンデンサ（C）
- **電流-電圧関係**: ${I_c = C {\times} \frac{d(V_c)}{dt}}$
- **離散時間**: ${V_c = V_{c\_old} + \frac{I_c {\times} dt}{C}}$
- **エネルギー**: ${E_c = \frac{1}{2} {\times} C {\times} V_c²}$

---

## 📁 出力ファイル

### 生成される出力ファイル
| ファイル | 内容 | 特徴 |
|----------|------|------|
| `advanced_equations_*.txt` | 完全ノード解析 | 全素子の詳細方程式・KCL解析 |
| `advanced_equations_*.tex` | LaTeX形式 | 論文・レポート用 |
| `special_equations_*.txt` | 一般化特殊解析 | **すべての回路**でKVL・エネルギー・システム行列解析 |

---

## 🔧 コマンドラインオプション

### 解析
```bash
python main.py [ファイル名] [オプション]
```

| オプション | 説明 |
|------------|------|
| `--no-latex` | LaTeX出力をスキップ |
| `--with-code` | Python コード生成を有効化（デフォルトは無効） |


---

## 📄 構文説明

| 要素 | 形式 | 例 |
|------|------|-----|
| 抵抗器 | `R 名前 ノード1 ノード2 値` | `R Ru1 1 2 Ru1` |
| インダクタ | `L 名前 ノード1 ノード2 値` | `L Lu1 2 3 Lu1` |
| コンデンサ | `C 名前 ノード1 ノード2 値` | `C Cu2 3 11 Cu2` |

---

## 🔄 一般化特殊解析システム

### すべての回路で実行される解析

#### 1. 包括的KVL解析
- 独立ループの自動特定
- 各ループのKVL方程式生成
- 例: ${V_{cu1} + V_{cv1} + V_{cw1} = 0}$

#### 2. エネルギー解析
- **磁気エネルギー**: ${E_L = \sum \frac{1}{2} L_i I_i²}$
- **電気エネルギー**: ${E_C = \sum \frac{1}{2} C_i V_i²}$
- **総エネルギー**: ${E_{total} = E_L + E_C}$

---

#### 3. システム行列解析
- 状態ベクトル: ${x = [I_{inductors}, V_{capacitors}]^T}$
- 状態空間表現: ${\frac{dx}{dt} = Ax + Bu}$

---

### ACF型回路の特殊解析

#### Primary System（個別インダクタンス値使用）
```
⎡d(ILu1)/dt⎤   ⎡Lu1+Ln1   Ln1     Ln1   ⎤⁻¹ ⎡Vu1 - Vn1 - Vcu2 - VRu1⎤
⎢d(ILv1)/dt⎥ = ⎢  Ln1   Lv1+Ln1   Ln1   ⎥   ⎢Vv1 - Vn1 - Vcv2 - VRv1⎥
⎣d(ILw1)/dt⎦   ⎣  Ln1     Ln1   Lw1+Ln1⎦   ⎣Vw1 - Vn1 - Vcw2 - VRw1⎦
```

#### Secondary System（個別インダクタンス値使用）
```
⎡d(ILu2)/dt⎤   ⎡Lu2+Ln2   Ln2     Ln2   ⎤⁻¹ ⎡Vn2 - Vn12 - VRu2⎤
⎢d(ILv2)/dt⎥ = ⎢  Ln2   Lv2+Ln2   Ln2   ⎥   ⎢Vn4 - Vn13 - VRv2⎥
⎣d(ILw2)/dt⎦   ⎣  Ln2     Ln2   Lw2+Ln2⎦   ⎣Vn6 - Vn14 - VRw2⎦
```

---

#### 電圧項の違いの説明

**Primary側に存在してSecondary側にない項:**
- **Vu1, Vv1, Vw1**: 外部電圧源（Secondary側は独立駆動されない）
- **Vcu2, Vcv2, Vcw2**: コンデンサ電圧（ノード電圧に含まれる）

**物理的意味:**
- Primary側: 外部電圧源による独立駆動
- Secondary側: Primary側からの磁気・容量結合による駆動
- 結合システム: 変圧器のような動作特性

---

#### 完全8×8行列システム（個別インダクタンス値）
```
⎡d(ILu1)/dt⎤   ⎡Lu1+Ln1  Ln1     Ln1     Ln1     0       0       0       0    ⎤⁻¹ ⎡VLu1⎤
⎢d(ILv1)/dt⎥   ⎢  Ln1   Lv1+Ln1  Ln1     Ln1     0       0       0       0    ⎥   ⎢VLv1⎥
⎢d(ILw1)/dt⎥   ⎢  Ln1     Ln1   Lw1+Ln1  Ln1     0       0       0       0    ⎥   ⎢VLw1⎥
⎢d(ILn1)/dt⎥ = ⎢  Ln1     Ln1     Ln1   Ln1+Ln2   0       0       0       Ln2  ⎥   ⎢VLn1⎥
⎢d(ILu2)/dt⎥   ⎢   0       0       0       0    Lu2+Ln2  Ln2     Ln2     Ln2  ⎥   ⎢VLu2⎥
⎢d(ILv2)/dt⎥   ⎢   0       0       0       0      Ln2   Lv2+Ln2  Ln2     Ln2  ⎥   ⎢VLv2⎥
⎢d(ILw2)/dt⎥   ⎢   0       0       0       0      Ln2     Ln2   Lw2+Ln2  Ln2  ⎥   ⎢VLw2⎥
⎣d(ILn2)/dt⎦   ⎣   0       0       0      Ln1     Ln2     Ln2     Ln2   Ln1+Ln2⎦   ⎣VLn2⎦
```

---

## 🔬 技術的詳細

### アルゴリズム
1. **回路ファイル解析**: .cctファイルの構文解析
2. **トポロジー構築**: ノード・素子接続グラフの作成
3. **完全ノード解析**: 各ノードの全接続素子の詳細解析
4. **素子別方程式生成**: R, L, C各素子の詳細方程式（個別値使用）
5. **動的解析**: 実際の回路構成に基づく関係式導出
6. **詳細KCL生成**: 各ノードの完全電流解析
7. **一般化特殊解析**: すべての回路でKVL・エネルギー・システム行列解析
8. **数式出力**: 複数形式での方程式生成

---

## 🔬 数学的基礎

### 基本法則
- **キルヒホッフの電流法則**: ${\sum I_{incoming} = \sum I_{outgoing}}$（全ノード適用）
- **キルヒホッフの電圧法則**: ${\sum V = 0}$（各ループ）
- **オームの法則**: ${V = IR}$, ${P = V²/R}$

### 素子方程式
- **抵抗器**: ${I = V/R}$, ${P = V²/R}$
- **インダクタ**: ${V = L {\times} \frac{dI}{dt}}$, ${E = \frac{1}{2} {\times} L {\times} I²}$
- **コンデンサ**: ${I = C {\times} \frac{dV}{dt}}$, ${E = \frac{1}{2} {\times} C {\times} V²}$

---

### 離散時間変換
- **インダクタ**: ${I = I_{old} + \frac{V {\times} dt}{L}}$
- **コンデンサ**: ${V = V_{old} + \frac{I {\times} dt}{C}}$

### 一般化特殊解析
- **包括的KVL解析**: すべての回路で独立ループ解析
- **エネルギー解析**: 磁気・電気エネルギーの総合解析
- **システム行列解析**: 状態空間表現による動的解析
- **容量結合**: 共有コンデンサによる結合
- **8×8行列システム**: 全インダクタの結合方程式

---

## � 一般化特殊解析の詳細

### すべての回路で実行される解析

#### 1. 包括的KVL解析
- **独立ループ特定**: 回路トポロジーから自動的にループを検出
- **KVL方程式生成**: 各ループで ${\sum V = 0}$ を適用
- **例**: コンデンサ回路 → ${V_{cu1} + V_{cv1} + V_{cw1} = 0}$

#### 2. エネルギー解析
- **磁気エネルギー**: ${E_L = \sum_{i} \frac{1}{2} L_i I_i²}$
- **電気エネルギー**: ${E_C = \sum_{i} \frac{1}{2} C_i V_i²}$
- **総システムエネルギー**: ${E_{total} = E_L + E_C}$
- **エネルギー保存**: システムの動的特性解析

#### 3. システム行列解析
- **状態ベクトル**: ${x = [I_{L1}, I_{L2}, ..., V_{C1}, V_{C2}, ...]^T}$
- **状態空間表現**: ${\frac{dx}{dt} = Ax + Bu}$, ${y = Cx + Du}$
- **システム行列**: 回路トポロジーに依存するA行列
- **動的解析**: 固有値・安定性解析の基礎

#### 4. ループ電流解析
- **ノード電流関係**: 各ノードでの ${\sum I_{incoming} = \sum I_{outgoing}}$
- **独立電流関係**: 回路構成に基づく電流依存関係
- **電流方向**: 自動的な電流方向決定

### ACF型回路の特殊解析

#### 個別インダクタンス値の使用
- **従来**: すべてのインダクタを共通値L1で近似
- **現在**: 各インダクタの実際の値（Lu1, Lv1, Lw1, Ln1, Lu2, Lv2, Lw2, Ln2）を使用
- **利点**: より正確な物理モデル、設計最適化の柔軟性

#### Primary/Secondary結合解析
- **Primary側**: 外部電圧源による独立駆動
- **Secondary側**: Primary側からの磁気・容量結合
- **結合行列**: 個別インダクタンス値を使用した8×8システム

---

## �💻 開発環境

### 必要環境
- Python 3.7+
- matplotlib（グラフ描画）
- numpy（数値計算）

---

## 🔄 適用可能な回路パターン

### パターン1: 直列接続
```
R → L → C → Ground
```
結果: ${I_C = I_L}$

### パターン2: 分岐接続
```
     → L2 → R2
L1 →
     → C → Ground
```
結果: ${I_C = I_{L1} - I_{L2}}$

---

### パターン3: 複数グラウンド接続
```
L1 → C1 ➘
L2 → C2 ➙ Ground
L3 → C3 ➚
```
結果: ${I_{C1} = I_{L1}}$, ${I_{C2} = I_{L2}}$, ${I_{C3} = I_{L3}}$


---

## 🧮 生成される数式の詳細

### 抵抗器
電圧: ${V_r = V_{node1} - V_{node2}}$
電流: ${I_r = V_r / R}$
離散: ${I_r = (V_{node1} - V_{node2}) / R}$

### インダクタ
電圧: ${V_l = V_{node1} - V_{node2}}$
電流: ${V_l = L {\times} \frac{d(I_l)}{dt}}$
離散: ${I_l = I_{l\_old} + \frac{V_l {\times} dt}{L}}$

---

### コンデンサ
電圧: ${V_c = V_{node1} - V_{node2}}$
電流: ${I_c = C {\times} \frac{d(V_c)}{dt}}$
離散: ${I_c = C {\times} \frac{(V_c - V_{c\_old})}{dt}}$

---

## 🧮 システム方程式

### キルヒホッフの電流法則（KCL）
各ノードで: ${\sum I_{incoming} = \sum I_{outgoing}}$

### キルヒホッフの電圧法則（KVL）
各ループで: ${\sum V = 0}$

---

## 📂 プロジェクト構造

```
📁 プロジェクトルート
├── 🚀 main.py                       # メインプログラム（高度解析機能）
├── 🔍 advanced_circuit_parser.py    # 高度回路解析エンジン
├── 📐 advanced_equation_generator.py # 高度数式生成エンジン
├── 🔍 circuit_parser.py             # 基本回路解析
├── 📐 equation_generator.py         # 基本数式生成
├── 📋 requirements.txt              # 依存関係
├── 📖 README.md                     # このファイル
├── 📁 input/                        # 回路ファイル
└── 📁 output/                       # 生成ファイル
```

---

## 📊 実装の改善点

### 🎯 一般化特殊解析
- **従来**: ACF2回路のみで特殊解析実行
- **現在**: **すべての回路**で包括的特殊解析を実行
- **新機能**: KVL解析、エネルギー解析、システム行列解析

### 🔧 個別リアクタンス値の採用
- **従来**: すべてのインダクタを共通値L1で近似
- **現在**: 各インダクタの実際の値を使用
- **利点**: より正確な物理モデル、設計最適化の柔軟性

### 📈 解析の拡張
- **包括的KVL**: 独立ループの自動検出と方程式生成
- **エネルギー解析**: 磁気・電気エネルギーの総合解析
- **システム行列**: 状態空間表現による動的解析
- **ループ電流**: ノード電流関係の詳細解析

これらの改善により、あらゆる回路構成に対して詳細で正確な数学的解析が可能になりました。
